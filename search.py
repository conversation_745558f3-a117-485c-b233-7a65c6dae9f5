import numpy as np
import queue
from game import BoardState, GameSimulator, Rules

class Problem:
    """
    This is an interface which GameStateProblem implements.
    You will be using GameStateProblem in your code. Please see
    GameStateProblem for details on the format of the inputs and
    outputs.
    """

    def __init__(self, initial_state, goal_state_set: set):
        self.initial_state = initial_state
        self.goal_state_set = goal_state_set

    def get_actions(self, state):
        """
        Returns a set of valid actions that can be taken from this state
        """
        pass

    def execute(self, state, action):
        """
        Transitions from the state to the next state that results from taking the action
        """
        pass

    def is_goal(self, state):
        """
        Checks if the state is a goal state in the set of goal states
        """
        return state in self.goal_state_set

class GameStateProblem(Problem):

    def __init__(self, initial_board_state, goal_board_state, player_idx):
        """
        player_idx is 0 or 1, depending on which player will be first to move from this initial state.

        Inputs for this constructor:
            - initial_board_state: an instance of BoardState
            - goal_board_state: an instance of BoardState
            - player_idx: an element from {0, 1}

        How Problem.initial_state and Problem.goal_state_set are represented:
            - initial_state: ((game board state tuple), player_idx ) <--- indicates state of board and who's turn it is to move
              ---specifically it is of the form: tuple( ( tuple(initial_board_state.state), player_idx ) )

            - goal_state_set: set([tuple((tuple(goal_board_state.state), 0)), tuple((tuple(goal_board_state.state), 1))])
              ---in otherwords, the goal_state_set allows the goal_board_state.state to be reached on either player 0 or player 1's
              turn.
        """
        super().__init__(tuple((tuple(initial_board_state.state), player_idx)), set([tuple((tuple(goal_board_state.state), 0)), tuple((tuple(goal_board_state.state), 1))]))
        self.sim = GameSimulator(None)
        self.search_alg_fnc = None
        self.set_search_alg()

    def set_search_alg(self, alg=""):
        """
        If you decide to implement several search algorithms, and you wish to switch between them,
        pass a string as a parameter to alg, and then set:
            self.search_alg_fnc = self.your_method
        to indicate which algorithm you'd like to run.

        TODO: You need to set self.search_alg_fnc here
        """
        if alg == "bfs" or alg == "":
            self.search_alg_fnc = self.breadth_first_search
        else:
            self.search_alg_fnc = self.breadth_first_search  # Default to BFS

    def get_actions(self, state: tuple):
        """
        From the given state, provide the set possible actions that can be taken from the state

        Inputs: 
            state: (encoded_state, player_idx), where encoded_state is a tuple of 12 integers,
                and player_idx is the player that is moving this turn

        Outputs:
            returns a set of actions
        """
        s, p = state
        np_state = np.array(s)
        self.sim.game_state.state = np_state
        self.sim.game_state.decode_state = self.sim.game_state.make_state()

        return self.sim.generate_valid_actions(p)

    def execute(self, state: tuple, action: tuple):
        """
        From the given state, executes the given action

        The action is given with respect to the current player

        Inputs: 
            state: is a tuple (encoded_state, player_idx), where encoded_state is a tuple of 12 integers,
                and player_idx is the player that is moving this turn
            action: (relative_idx, position), where relative_idx is an index into the encoded_state
                with respect to the player_idx, and position is the encoded position where the indexed piece should move to.
        Outputs:
            the next state tuple that results from taking action in state
        """
        s, p = state
        k, v = action
        offset_idx = p * 6
        return tuple((tuple( s[i] if i != offset_idx + k else v for i in range(len(s))), (p + 1) % 2))

    def breadth_first_search(self):
        """
        Implements Breadth-First Search to find the optimal (shortest) path from initial state to goal state.

        Returns:
            List of (state, action) pairs representing the optimal solution path.
            Format: [(s1, a1), (s2, a2), ..., (sN, None)] where sN is a goal state.
        """
        from collections import deque

        # Initialize BFS
        initial_state = self.initial_state

        # Check if initial state is already a goal
        if self.is_goal(initial_state):
            return [(initial_state, None)]

        # BFS data structures
        queue = deque([(initial_state, [])])  # (state, path_to_state)
        visited = set([initial_state])

        while queue:
            current_state, path = queue.popleft()

            # Get all valid actions from current state
            actions = self.get_actions(current_state)

            # Try each action
            for action in actions:
                # Execute action to get next state
                next_state = self.execute(current_state, action)

                # Skip if we've already visited this state
                if next_state in visited:
                    continue

                # Mark as visited
                visited.add(next_state)

                # Create path to next state
                next_path = path + [(current_state, action)]

                # Check if we've reached a goal state
                if self.is_goal(next_state):
                    # Return complete solution path
                    return next_path + [(next_state, None)]

                # Add to queue for further exploration
                queue.append((next_state, next_path))

        # No solution found
        return []

