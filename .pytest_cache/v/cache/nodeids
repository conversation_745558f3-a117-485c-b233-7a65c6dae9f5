["test_search.py::TestSearch::test_ball_reachability[state0-reachable0-0]", "test_search.py::TestSearch::test_ball_reachability[state1-reachable1-1]", "test_search.py::TestSearch::test_ball_reachability[state10-reachable10-1]", "test_search.py::TestSearch::test_ball_reachability[state11-reachable11-1]", "test_search.py::TestSearch::test_ball_reachability[state12-reachable12-1]", "test_search.py::TestSearch::test_ball_reachability[state13-reachable13-1]", "test_search.py::TestSearch::test_ball_reachability[state14-reachable14-1]", "test_search.py::TestSearch::test_ball_reachability[state15-reachable15-1]", "test_search.py::TestSearch::test_ball_reachability[state16-reachable16-1]", "test_search.py::TestSearch::test_ball_reachability[state17-reachable17-1]", "test_search.py::TestSearch::test_ball_reachability[state18-reachable18-0]", "test_search.py::TestSearch::test_ball_reachability[state19-reachable19-0]", "test_search.py::TestSearch::test_ball_reachability[state2-reachable2-1]", "test_search.py::TestSearch::test_ball_reachability[state20-reachable20-0]", "test_search.py::TestSearch::test_ball_reachability[state21-reachable21-0]", "test_search.py::TestSearch::test_ball_reachability[state22-reachable22-0]", "test_search.py::TestSearch::test_ball_reachability[state3-reachable3-0]", "test_search.py::TestSearch::test_ball_reachability[state4-reachable4-0]", "test_search.py::TestSearch::test_ball_reachability[state5-reachable5-0]", "test_search.py::TestSearch::test_ball_reachability[state6-reachable6-0]", "test_search.py::TestSearch::test_ball_reachability[state7-reachable7-0]", "test_search.py::TestSearch::test_ball_reachability[state8-reachable8-1]", "test_search.py::TestSearch::test_ball_reachability[state9-reachable9-1]", "test_search.py::TestSearch::test_encoded_decode", "test_search.py::TestSearch::test_game_state_goal_state", "test_search.py::TestSearch::test_game_state_problem[0]", "test_search.py::TestSearch::test_game_state_problem[1]", "test_search.py::TestSearch::test_generate_actions", "test_search.py::TestSearch::test_initial_state", "test_search.py::TestSearch::test_is_valid", "test_search.py::TestSearch::test_termination_state[state0-False]", "test_search.py::TestSearch::test_termination_state[state1-False]", "test_search.py::TestSearch::test_termination_state[state2-False]", "test_search.py::TestSearch::test_termination_state[state3-True]", "test_search.py::TestSearch::test_termination_state[state4-True]", "test_search.py::TestSearch::test_termination_state[state5-False]", "test_search.py::TestSearch::test_validate_action[action0-0-True-]", "test_search.py::TestSearch::test_validate_action[action1-0-True-]", "test_search.py::TestSearch::test_validate_action[action2-0-True-]", "test_search.py::TestSearch::test_validate_action[action3-0-True-]", "test_search.py::TestSearch::test_validate_action[action4-0-True-]", "test_search.py::TestSearch::test_validate_action[action5-0-True-]", "test_search.py::TestSearch::test_validate_action[action6-0-True-]"]